# Data Manipulation and Basic Libraries
import numpy as np
import pandas as pd
import random
import time
import math

# PyTorch
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

# Scikit-learn
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix, log_loss
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import ExtraTreesClassifier, GradientBoostingClassifier, RandomForestClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.neural_network import MLPClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.model_selection import StratifiedKFold # For Optuna cross-validation

# Optuna for hyperparameter tuning
import optuna

# Plotting Libraries
import matplotlib.pyplot as plt
import seaborn as sns

# Utilities
from tqdm.notebook import tqdm # For progress bars in notebook
import warnings
warnings.filterwarnings('ignore')

# Display options
pd.set_option('display.max_columns', None)

# Ensure reproducibility where possible
SEED = 42
random.seed(SEED)
np.random.seed(SEED)
torch.manual_seed(SEED)
if torch.cuda.is_available():
    torch.cuda.manual_seed_all(SEED)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# Device configuration
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

CONFIG = {
    'random_seed': SEED,
    'base_path': '/kaggle/input/processed_data_paper_v7_improved/', 
    
    'train_features_orig_csv': 'train_features_paper_original_scaled.csv', # This will be normalized
    'train_labels_orig_csv': 'train_labels_paper_original.csv',
    # SMOTEd data is not used for training in this version as per request
    'val_features_scaled_csv': 'val_features_paper_scaled.csv', # This will be normalized
    'val_labels_csv': 'val_labels_paper.csv',
    'test_features_scaled_csv': 'test_features_paper_scaled.csv', # This will be normalized
    'test_labels_csv': 'test_labels_paper.csv',
    'feature_names_txt': 'feature_names_paper.txt',
    
    'lstm_timesteps': 1, 
    'num_classes': 3, 
    
    'optuna_n_trials_lstm': 20, # Reduced for faster overall run, increase for better tuning
    'optuna_n_trials_classical': 15, # Reduced for faster overall run
    'optuna_epochs_lstm': 25, 
    'optuna_early_stopping_patience_lstm': 5,
    'optuna_timeout_per_study': 1800, 
    'optuna_cv_folds_classical': 3, # Cross-validation folds for classical model tuning

    'final_model_epochs_lstm': 100, 
    'final_model_early_stopping_patience_lstm': 10,
    
    # Classical model default params (Optuna will search around these or in defined ranges)
    'et_class_weight': 'balanced',
    'lr_solver': 'liblinear', 
    'lr_class_weight': 'balanced',
    'svc_class_weight': 'balanced',
    'svc_probability': True,
    'rf_class_weight': 'balanced',
    'mlp_early_stopping': True, 
    'mlp_validation_fraction': 0.1, 
    'mlp_n_iter_no_change': 10
}

class_labels = ['Draw', 'Home Win', 'Away Win']
results_summary = [] # List of dicts for summary

print("--- Loading and Normalizing Preprocessed Data (Paper Features) --- ")
datasets_pd = {}
datasets_np = {}
feature_names = []
INPUT_SIZE = 0 
base_path = CONFIG['base_path']
data_loaded_successfully = False

try:
    with open(base_path + CONFIG['feature_names_txt'], 'r') as f:
        feature_names = [line.strip() for line in f.readlines()]
    print(f"Loaded {len(feature_names)} feature names from {CONFIG['feature_names_txt']}.")
    
    if not feature_names:
        raise ValueError("Feature names list is empty. Cannot proceed.")

    INPUT_SIZE = len(feature_names) // CONFIG['lstm_timesteps']
    if len(feature_names) % CONFIG['lstm_timesteps'] != 0 and CONFIG['lstm_timesteps'] != 1:
        raise ValueError(f"Number of features ({len(feature_names)}) must be divisible by timesteps ({CONFIG['lstm_timesteps']}) or timesteps should be 1.")
    print(f"Input Size (features per timestep): {INPUT_SIZE}")

    # Load data as pandas DataFrames first for easier handling
    datasets_pd['X_train_orig'] = pd.read_csv(base_path + CONFIG['train_features_orig_csv'], header=0, names=feature_names)
    datasets_pd['y_train_orig'] = pd.read_csv(base_path + CONFIG['train_labels_orig_csv'])['target']
    datasets_pd['X_val'] = pd.read_csv(base_path + CONFIG['val_features_scaled_csv'], header=0, names=feature_names)
    datasets_pd['y_val'] = pd.read_csv(base_path + CONFIG['val_labels_csv'])['target']
    datasets_pd['X_test'] = pd.read_csv(base_path + CONFIG['test_features_scaled_csv'], header=0, names=feature_names)
    datasets_pd['y_test'] = pd.read_csv(base_path + CONFIG['test_labels_csv'])['target']

    # Normalize features
    scaler = StandardScaler()
    datasets_np['X_train_orig_norm'] = scaler.fit_transform(datasets_pd['X_train_orig'])
    datasets_np['X_val_norm'] = scaler.transform(datasets_pd['X_val'])
    datasets_np['X_test_norm'] = scaler.transform(datasets_pd['X_test'])
    
    # Convert labels to NumPy arrays
    datasets_np['y_train_orig'] = datasets_pd['y_train_orig'].values
    datasets_np['y_val'] = datasets_pd['y_val'].values
    datasets_np['y_test'] = datasets_pd['y_test'].values

    print("Data shapes (NumPy arrays after normalization):")
    print(f"  X_train_orig_norm: {datasets_np['X_train_orig_norm'].shape}, y_train_orig: {datasets_np['y_train_orig'].shape}")
    print(f"  X_val_norm: {datasets_np['X_val_norm'].shape}, y_val: {datasets_np['y_val'].shape}")
    print(f"  X_test_norm: {datasets_np['X_test_norm'].shape}, y_test: {datasets_np['y_test'].shape}")
    print("Data loaded and normalized successfully.")
    data_loaded_successfully = True

except FileNotFoundError as e:
    print(f"Error: One or more data files not found. {e}")
    print(f"Please ensure the CSV files are in '{base_path}' and filenames in CONFIG are correct.")
except ValueError as e:
    print(f"ValueError during data loading/preprocessing: {e}")
except Exception as e:
    print(f"An unexpected error occurred during data loading: {e}")

class SoccerDataset(Dataset):
    def __init__(self, features, labels, timesteps=1):
        self.features = features
        self.labels = labels
        self.timesteps = timesteps
        if features.shape[0] > 0 and features.shape[1] > 0: 
            self.n_features_per_timestep = self.features.shape[1] // self.timesteps
        else:
            self.n_features_per_timestep = 0 

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        if self.features.shape[0] == 0 or self.n_features_per_timestep == 0: 
            return torch.empty(0), torch.tensor(self.labels[idx], dtype=torch.long)
        
        current_features = self.features[idx].reshape(self.timesteps, self.n_features_per_timestep)
        return torch.tensor(current_features, dtype=torch.float32), torch.tensor(self.labels[idx], dtype=torch.long)

if data_loaded_successfully and INPUT_SIZE > 0:
    train_ds_orig_norm_lstm = SoccerDataset(datasets_np['X_train_orig_norm'], datasets_np['y_train_orig'], timesteps=CONFIG['lstm_timesteps'])
    val_ds_norm_lstm = SoccerDataset(datasets_np['X_val_norm'], datasets_np['y_val'], timesteps=CONFIG['lstm_timesteps'])
    test_ds_norm_lstm = SoccerDataset(datasets_np['X_test_norm'], datasets_np['y_test'], timesteps=CONFIG['lstm_timesteps'])

    print(f"LSTM Original Normalized Training samples: {len(train_ds_orig_norm_lstm)}")
    print(f"LSTM Normalized Validation samples: {len(val_ds_norm_lstm)}")
    print(f"LSTM Normalized Test samples: {len(test_ds_norm_lstm)}")
else:
    print("Data not loaded successfully or INPUT_SIZE is 0, skipping PyTorch dataset creation.")

class LSTMModel(nn.Module):
    def __init__(self, input_size, rnn_size, num_lstm_layers, num_dense_layers, dense_units, dropout_rate, output_size):
        super(LSTMModel, self).__init__()
        self.num_lstm_layers = num_lstm_layers
        self.rnn_size = rnn_size
        
        self.lstm_layers_list = nn.ModuleList()
        current_input_size = input_size
        for i in range(num_lstm_layers):
            lstm_dropout = dropout_rate if num_lstm_layers > 1 and i < num_lstm_layers - 1 else 0
            self.lstm_layers_list.append(nn.LSTM(current_input_size, rnn_size, batch_first=True, dropout=lstm_dropout))
            current_input_size = rnn_size 
        
        self.dropout_after_lstm = nn.Dropout(dropout_rate)
        
        self.dense_layers_seq = nn.Sequential()
        current_dense_input = rnn_size
        if num_dense_layers > 0:
            for i in range(num_dense_layers):
                self.dense_layers_seq.add_module(f"dense_{i}", nn.Linear(current_dense_input, dense_units))
                self.dense_layers_seq.add_module(f"relu_{i}", nn.ReLU())
                self.dense_layers_seq.add_module(f"dropout_dense_{i}", nn.Dropout(dropout_rate))
                current_dense_input = dense_units
            
        self.fc_out = nn.Linear(current_dense_input, output_size)

    def forward(self, x):
        lstm_out = x
        for lstm_layer in self.lstm_layers_list:
            lstm_out, _ = lstm_layer(lstm_out) 
        
        dense_input = lstm_out[:, -1, :] 
        dense_input = self.dropout_after_lstm(dense_input)
        
        if len(self.dense_layers_seq) > 0:
             dense_input = self.dense_layers_seq(dense_input)
            
        out = self.fc_out(dense_input)
        return out

def train_epoch_fn_lstm(model, dataloader, criterion, optimizer, device):
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    for features, labels in dataloader:
        features, labels = features.to(device), labels.to(device)
        optimizer.zero_grad()
        outputs = model(features)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()
        running_loss += loss.item() * features.size(0)
        _, predicted = torch.max(outputs.data, 1)
        total_samples += labels.size(0)
        correct_predictions += (predicted == labels).sum().item()
    epoch_loss = running_loss / total_samples if total_samples > 0 else 0
    epoch_acc = correct_predictions / total_samples if total_samples > 0 else 0
    return epoch_loss, epoch_acc

def evaluate_model_fn_lstm(model, dataloader, criterion, device, return_probs=False):
    model.eval()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    all_preds_list = []
    all_labels_list = []
    all_probs_list = []
    with torch.no_grad():
        for features, labels in dataloader:
            features, labels = features.to(device), labels.to(device)
            outputs = model(features)
            loss = criterion(outputs, labels)
            running_loss += loss.item() * features.size(0)
            _, predicted = torch.max(outputs.data, 1)
            if return_probs:
                probs = torch.softmax(outputs, dim=1)
                all_probs_list.append(probs.cpu().numpy())
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()
            all_preds_list.extend(predicted.cpu().numpy())
            all_labels_list.extend(labels.cpu().numpy())
    epoch_loss = running_loss / total_samples if total_samples > 0 else 0
    epoch_acc = correct_predictions / total_samples if total_samples > 0 else 0
    if return_probs:
        all_probs_np = np.concatenate(all_probs_list, axis=0) if all_probs_list else np.empty((0, CONFIG['num_classes']))
        return epoch_loss, epoch_acc, all_labels_list, all_preds_list, all_probs_np
    return epoch_loss, epoch_acc, all_labels_list, all_preds_list

def plot_confusion_matrix_generic(y_true, y_pred, title_suffix, labels_list=class_labels):
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(6, 4))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=labels_list, yticklabels=labels_list)
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title(f'Confusion Matrix - {title_suffix}')
    plt.show()

def evaluate_sklearn_model(model, X_test_data, y_test_data, model_name_str, training_data_type_str, class_labels_list, results_list_summary, return_probs=False):
    print(f"\n--- Evaluating {model_name_str} (Trained on {training_data_type_str} Data) on Test Set ---")
    y_pred_test = model.predict(X_test_data)
    test_accuracy = accuracy_score(y_test_data, y_pred_test)
    y_pred_proba_test = None
    if return_probs and hasattr(model, "predict_proba"):
        y_pred_proba_test = model.predict_proba(X_test_data)

    print(f"{model_name_str} ({training_data_type_str}) Test Accuracy: {test_accuracy:.4f}")
    print(f"\n{model_name_str} ({training_data_type_str}) Classification Report:")
    print(classification_report(y_test_data, y_pred_test, target_names=class_labels_list, zero_division=0))
    
    plot_confusion_matrix_generic(y_test_data, y_pred_test, f"{model_name_str} - {training_data_type_str} Test Set", labels_list=class_labels_list)
    
    results_list_summary.append({
        'Model': model_name_str,
        'Training_Data_Type': training_data_type_str,
        'Test_Accuracy': test_accuracy
    })
    
    if return_probs:
        return test_accuracy, y_pred_test, y_pred_proba_test
    return test_accuracy, y_pred_test

def plot_feature_importances_sklearn(model, feature_names_list, model_name_str, top_k=20):
    if hasattr(model, 'feature_importances_') and feature_names_list:
        importances = model.feature_importances_
        indices = np.argsort(importances)[::-1]
        actual_top_k = min(top_k, len(feature_names_list))
        plt.figure(figsize=(12, 8))
        plt.title(f"Top {actual_top_k} Feature Importances ({model_name_str})")
        plt.bar(range(actual_top_k), importances[indices][:actual_top_k], align="center")
        plt.xticks(range(actual_top_k), np.array(feature_names_list)[indices][:actual_top_k], rotation=90)
        plt.xlim([-1, actual_top_k])
        plt.ylabel("Importance")
        plt.tight_layout()
        plt.show()
    elif hasattr(model, 'coef_') and feature_names_list: # For linear models
        if model.coef_.ndim > 1: # Multi-class case
            importances = np.sum(np.abs(model.coef_), axis=0)
        else: # Binary or single output regression
            importances = np.abs(model.coef_)
        indices = np.argsort(importances)[::-1]
        actual_top_k = min(top_k, len(feature_names_list))
        plt.figure(figsize=(12, 8))
        plt.title(f"Top {actual_top_k} Feature Importances (Coefficient Magnitude - {model_name_str})")
        plt.bar(range(actual_top_k), importances[indices][:actual_top_k], align="center")
        plt.xticks(range(actual_top_k), np.array(feature_names_list)[indices][:actual_top_k], rotation=90)
        plt.xlim([-1, actual_top_k])
        plt.ylabel("Coefficient Magnitude")
        plt.tight_layout()
        plt.show()
    else:
        print(f"Model {model_name_str} does not have feature_importances_ or coef_ attribute, or feature_names are missing.")

def optuna_objective_lstm(trial, X_train_data, y_train_data, X_val_data, y_val_data):
    if INPUT_SIZE == 0: 
        print("Warning: INPUT_SIZE is 0 in optuna_objective_lstm. Returning 0.0 accuracy.")
        return 0.0
        
    rnn_size = trial.suggest_categorical('rnn_size', [32, 64, 96, 128])
    num_lstm_layers = trial.suggest_int('num_lstm_layers', 1, 2)
    num_dense_layers = trial.suggest_int('num_dense_layers', 0, 2) 
    dense_units = trial.suggest_categorical('dense_units', [32, 64, 128]) if num_dense_layers > 0 else 32
    dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.5, step=0.05)
    learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)
    batch_size_trial = trial.suggest_categorical('batch_size', [32, 64, 128])
    weight_decay = trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True)

    trial_train_dataset = SoccerDataset(X_train_data, y_train_data, timesteps=CONFIG['lstm_timesteps'])
    trial_val_dataset = SoccerDataset(X_val_data, y_val_data, timesteps=CONFIG['lstm_timesteps'])
    train_loader_opt = DataLoader(trial_train_dataset, batch_size=batch_size_trial, shuffle=True)
    val_loader_opt = DataLoader(trial_val_dataset, batch_size=batch_size_trial, shuffle=False)

    model = LSTMModel(
        input_size=INPUT_SIZE,
        rnn_size=rnn_size,
        num_lstm_layers=num_lstm_layers,
        num_dense_layers=num_dense_layers,
        dense_units=dense_units,
        dropout_rate=dropout_rate,
        output_size=CONFIG['num_classes']
    ).to(device)
    
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)

    best_val_accuracy_trial = 0.0
    epochs_no_improve_trial = 0

    for epoch in range(CONFIG['optuna_epochs_lstm']):
        train_loss, train_acc = train_epoch_fn_lstm(model, train_loader_opt, criterion, optimizer, device)
        val_loss, val_acc, _, _ = evaluate_model_fn_lstm(model, val_loader_opt, criterion, device)
        
        if val_acc > best_val_accuracy_trial:
            best_val_accuracy_trial = val_acc
            epochs_no_improve_trial = 0
        else:
            epochs_no_improve_trial += 1
        
        trial.report(val_acc, epoch)
        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()
        if epochs_no_improve_trial >= CONFIG['optuna_early_stopping_patience_lstm']:
            break
            
    return best_val_accuracy_trial

def optuna_objective_classical(trial, X_train_data, y_train_data, model_type):
    if X_train_data.shape[0] == 0: return 0.0
    
    cv_scores = []
    # Using StratifiedKFold for classification tasks to maintain class proportions
    skf = StratifiedKFold(n_splits=CONFIG['optuna_cv_folds_classical'], shuffle=True, random_state=CONFIG['random_seed'])

    for fold, (train_idx, val_idx) in enumerate(skf.split(X_train_data, y_train_data)):
        X_train_fold, X_val_fold = X_train_data[train_idx], X_train_data[val_idx]
        y_train_fold, y_val_fold = y_train_data[train_idx], y_train_data[val_idx]
        
        if model_type == 'ExtraTreesClassifier':
            params = {
                'n_estimators': trial.suggest_int('et_n_estimators', 50, 400, step=50),
                'max_depth': trial.suggest_int('et_max_depth', 10, 30, step=5),
                'min_samples_split': trial.suggest_int('et_min_samples_split', 2, 10),
                'min_samples_leaf': trial.suggest_int('et_min_samples_leaf', 1, 5),
                'class_weight': CONFIG['et_class_weight'],
                'random_state': CONFIG['random_seed'], 'n_jobs': -1
            }
            model = ExtraTreesClassifier(**params)
        elif model_type == 'LogisticRegression':
            params = {
                'C': trial.suggest_float('lr_C', 1e-3, 1e1, log=True),
                'solver': CONFIG['lr_solver'], 'class_weight': CONFIG['lr_class_weight'],
                'max_iter': trial.suggest_int('lr_max_iter', 100, 500, step=100),
                'random_state': CONFIG['random_seed']
            }
            model = LogisticRegression(**params)
        elif model_type == 'SVC':
            params = {
                'C': trial.suggest_float('svc_C', 1e-2, 1e2, log=True),
                'kernel': trial.suggest_categorical('svc_kernel', ['rbf', 'linear', 'poly']),
                'gamma': trial.suggest_categorical('svc_gamma', ['scale', 'auto']),
                'class_weight': CONFIG['svc_class_weight'], 'probability': CONFIG['svc_probability'],
                'random_state': CONFIG['random_seed']
            }
            if params['kernel'] == 'poly':
                params['degree'] = trial.suggest_int('svc_degree', 2, 4)
            model = SVC(**params)
        elif model_type == 'GradientBoostingClassifier':
            params = {
                'n_estimators': trial.suggest_int('gb_n_estimators', 50, 300, step=50),
                'learning_rate': trial.suggest_float('gb_learning_rate', 0.01, 0.2, log=True),
                'max_depth': trial.suggest_int('gb_max_depth', 2, 5),
                'subsample': trial.suggest_float('gb_subsample', 0.6, 1.0, step=0.1),
                'random_state': CONFIG['random_seed']
            }
            model = GradientBoostingClassifier(**params)
        elif model_type == 'RandomForestClassifier':
            params = {
                'n_estimators': trial.suggest_int('rf_n_estimators', 50, 400, step=50),
                'max_depth': trial.suggest_int('rf_max_depth', 10, 30, step=5),
                'min_samples_split': trial.suggest_int('rf_min_samples_split', 2, 10),
                'min_samples_leaf': trial.suggest_int('rf_min_samples_leaf', 1, 5),
                'class_weight': CONFIG['rf_class_weight'],
                'random_state': CONFIG['random_seed'], 'n_jobs': -1
            }
            model = RandomForestClassifier(**params)
        elif model_type == 'MLPClassifier':
            params = {
                'hidden_layer_sizes': trial.suggest_categorical('mlp_hidden_layer_sizes', [(32,), (64,), (32,16), (64,32)]),
                'activation': trial.suggest_categorical('mlp_activation', ['relu', 'tanh']),
                'solver': trial.suggest_categorical('mlp_solver', ['adam', 'sgd']),
                'alpha': trial.suggest_float('mlp_alpha', 1e-5, 1e-2, log=True),
                'learning_rate_init': trial.suggest_float('mlp_learning_rate_init', 1e-4, 1e-2, log=True),
                'max_iter': trial.suggest_int('mlp_max_iter', 200, 600, step=100),
                'early_stopping': CONFIG['mlp_early_stopping'], 
                'validation_fraction': CONFIG['mlp_validation_fraction'],
                'n_iter_no_change': CONFIG['mlp_n_iter_no_change'],
                'random_state': CONFIG['random_seed']
            }
            model = MLPClassifier(**params)
        elif model_type == 'KNeighborsClassifier':
            params = {
                'n_neighbors': trial.suggest_int('knn_n_neighbors', 3, 21, step=2),
                'weights': trial.suggest_categorical('knn_weights', ['uniform', 'distance']),
                'metric': trial.suggest_categorical('knn_metric', ['minkowski', 'euclidean', 'manhattan']),
                'n_jobs': -1
            }
            if params['metric'] == 'minkowski':
                 params['p'] = trial.suggest_int('knn_p', 1, 2)
            model = KNeighborsClassifier(**params)
        elif model_type == 'GaussianNB':
            params = {'var_smoothing': trial.suggest_float('gnb_var_smoothing', 1e-10, 1e-7, log=True)}
            model = GaussianNB(**params)
        else:
            raise ValueError(f"Unsupported model_type: {model_type}")

        model.fit(X_train_fold, y_train_fold)
        val_preds = model.predict(X_val_fold)
        cv_scores.append(accuracy_score(y_val_fold, val_preds))
    
    avg_cv_accuracy = np.mean(cv_scores)
    trial.report(avg_cv_accuracy, fold) # Report final average accuracy
    if trial.should_prune(): # Pruning based on intermediate folds is harder with sklearn CV loop
        raise optuna.exceptions.TrialPruned()
        
    return avg_cv_accuracy

def run_model_experiment(model_type_name, X_train_full, y_train_full, X_val_data, y_val_data, X_test_data, y_test_data, results_list_summary_ref, is_lstm=False):
    """Runs Optuna tuning, trains the best model, and evaluates it. Stores test predictions."""
    print(f"\n======= Starting Experiment for: {model_type_name} (Original Normalized Data) =======")
    
    if (is_lstm and INPUT_SIZE == 0) or X_train_full.shape[0] == 0:
        print(f"{model_type_name}: INPUT_SIZE is 0 or no training data, cannot proceed.")
        results_list_summary_ref.append({
            'Model': model_type_name,
            'Training_Data_Type': 'Original_Normalized',
            'Test_Accuracy': 0.0,
            'Best_Params': 'N/A',
            'Test_Pred_Proba': None
        })
        return None, {}

    # Optuna Study
    study = optuna.create_study(direction='maximize', pruner=optuna.pruners.MedianPruner(n_startup_trials=3, n_warmup_steps=3, interval_steps=1))
    
    if is_lstm:
        objective_func = lambda trial: optuna_objective_lstm(trial, X_train_full, y_train_full, X_val_data, y_val_data)
        n_trials = CONFIG['optuna_n_trials_lstm']
    else:
        # For classical models, Optuna uses cross-validation on the full training set (X_train_full, y_train_full)
        # The X_val_data, y_val_data are not directly used by optuna_objective_classical but kept for consistency in run_model_experiment signature
        objective_func = lambda trial: optuna_objective_classical(trial, X_train_full, y_train_full, model_type_name) 
        n_trials = CONFIG['optuna_n_trials_classical']

    study.optimize(objective_func, n_trials=n_trials, timeout=CONFIG['optuna_timeout_per_study'], show_progress_bar=True)

    print(f"\n--- Optuna Study Summary ({model_type_name}) ---")
    best_trial = study.best_trial
    print(f"Best trial value (Validation Accuracy / CV Score): {best_trial.value:.4f}")
    print("Best hyperparameters:")
    best_hps = {}
    for key, value in best_trial.params.items():
        print(f"  {key}: {value}")
        # Store HPs without the model prefix for cleaner use later
        param_key_cleaned = key.split('_', 1)[1] if '_' in key and not key.startswith('n_') and not key.startswith('hidden') else key
        best_hps[param_key_cleaned] = value

    # Train Best Model on full original training data
    print(f"\n--- Training Best {model_type_name} Model ---")
    
    final_model = None
    test_pred_proba = None

    if is_lstm:
        final_model = LSTMModel(
            input_size=INPUT_SIZE, rnn_size=best_hps.get('rnn_size',64), 
            num_lstm_layers=best_hps.get('num_lstm_layers',1), 
            num_dense_layers=best_hps.get('num_dense_layers',0), 
            dense_units=best_hps.get('dense_units',32), 
            dropout_rate=best_hps.get('dropout_rate',0.2), 
            output_size=CONFIG['num_classes']
        ).to(device)
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(final_model.parameters(), lr=best_hps.get('learning_rate',0.001), weight_decay=best_hps.get('weight_decay',1e-5))
        
        final_train_ds = SoccerDataset(X_train_full, y_train_full, timesteps=CONFIG['lstm_timesteps'])
        final_val_ds = SoccerDataset(X_val_data, y_val_data, timesteps=CONFIG['lstm_timesteps'])
        final_test_ds = SoccerDataset(X_test_data, y_test_data, timesteps=CONFIG['lstm_timesteps'])
        
        final_train_loader = DataLoader(final_train_ds, batch_size=best_hps.get('batch_size',64), shuffle=True)
        final_val_loader = DataLoader(final_val_ds, batch_size=best_hps.get('batch_size',64), shuffle=False)
        final_test_loader = DataLoader(final_test_ds, batch_size=best_hps.get('batch_size',64), shuffle=False)

        best_val_acc_final = 0.0
        epochs_no_improve = 0
        history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}

        for epoch in tqdm(range(CONFIG['final_model_epochs_lstm']), desc=f"Training Best {model_type_name}"):
            train_loss, train_acc = train_epoch_fn_lstm(final_model, final_train_loader, criterion, optimizer, device)
            val_loss, val_acc, _, _ = evaluate_model_fn_lstm(final_model, final_val_loader, criterion, device)
            history['train_loss'].append(train_loss); history['val_loss'].append(val_loss)
            history['train_acc'].append(train_acc); history['val_acc'].append(val_acc)
            print(f"Epoch {epoch+1}/{CONFIG['final_model_epochs_lstm']} -> Train Loss: {train_loss:.4f}, Acc: {train_acc:.4f} | Val Loss: {val_loss:.4f}, Acc: {val_acc:.4f}")
            if val_acc > best_val_acc_final:
                best_val_acc_final = val_acc
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1
            if epochs_no_improve >= CONFIG['final_model_early_stopping_patience_lstm']:
                print(f"Early stopping at epoch {epoch+1}.")
                break
        # Plot LSTM training curves
        plt.figure(figsize=(12, 4))
        plt.subplot(1, 2, 1); plt.plot(history['train_loss'], label='Train Loss'); plt.plot(history['val_loss'], label='Val Loss'); plt.legend(); plt.title('Loss')
        plt.subplot(1, 2, 2); plt.plot(history['train_acc'], label='Train Acc'); plt.plot(history['val_acc'], label='Val Acc'); plt.legend(); plt.title('Accuracy')
        plt.show()
        # LSTM Evaluation
        print(f"\n--- Final Evaluation LSTM ({model_type_name}) ---")
        _, train_acc_f, train_true, train_pred = evaluate_model_fn_lstm(final_model, final_train_loader, criterion, device)
        print(f"Final LSTM Training Accuracy: {train_acc_f:.4f}")
        print(classification_report(train_true, train_pred, target_names=class_labels, zero_division=0))
        plot_confusion_matrix_generic(train_true, train_pred, f"LSTM - Training Set")
        
        _, val_acc_f, val_true, val_pred = evaluate_model_fn_lstm(final_model, final_val_loader, criterion, device)
        print(f"Final LSTM Validation Accuracy: {val_acc_f:.4f}")
        print(classification_report(val_true, val_pred, target_names=class_labels, zero_division=0))
        plot_confusion_matrix_generic(val_true, val_pred, f"LSTM - Validation Set")
        
        _, test_acc_f, test_true, test_pred, test_pred_proba = evaluate_model_fn_lstm(final_model, final_test_loader, criterion, device, return_probs=True)
        print(f"Final LSTM Test Accuracy: {test_acc_f:.4f}")
        print(classification_report(test_true, test_pred, target_names=class_labels, zero_division=0))
        plot_confusion_matrix_generic(test_true, test_pred, f"LSTM - Test Set")
        results_list_summary_ref[-1]['Test_Accuracy'] = test_acc_f # Update accuracy in summary
        results_list_summary_ref[-1]['Test_Pred_Proba'] = test_pred_proba
        results_list_summary_ref[-1]['Best_Params'] = best_hps
        
    else: # Classical models
        # Reconstruct params for classical models from best_trial.params
        # Optuna prefixes params with model type, e.g., 'et_n_estimators'. We need to strip that for direct use.
        # Or, better, pass the full best_trial.params to the constructor if it accepts extra args or filter them.
        # For simplicity, we'll reconstruct based on known prefixes.
        
        model_params_final = {}
        prefix_map = { # map optuna trial param name to actual model param name
            'ExtraTreesClassifier': 'et_',
            'LogisticRegression': 'lr_',
            'SVC': 'svc_',
            'GradientBoostingClassifier': 'gb_',
            'RandomForestClassifier': 'rf_',
            'MLPClassifier': 'mlp_',
            'KNeighborsClassifier': 'knn_',
            'GaussianNB': 'gnb_'
        }
        current_prefix = prefix_map.get(model_type_name, '')
        for key, value in best_trial.params.items():
            if key.startswith(current_prefix):
                model_params_final[key[len(current_prefix):]] = value
            else: # For params that might not have prefix in optuna (like knn_p)
                 model_params_final[key] = value
        
        # Add fixed params not tuned by Optuna but needed by model
        model_params_final['random_state'] = CONFIG['random_seed']
        if model_type_name in ['ExtraTreesClassifier', 'RandomForestClassifier', 'KNeighborsClassifier']:
            model_params_final['n_jobs'] = -1
        if model_type_name in ['ExtraTreesClassifier', 'LogisticRegression', 'SVC', 'RandomForestClassifier']:
             model_params_final['class_weight'] = CONFIG.get(f"{current_prefix}class_weight", 'balanced')
        if model_type_name == 'SVC':
            model_params_final['probability'] = CONFIG['svc_probability']
        if model_type_name == 'MLPClassifier':
            model_params_final['early_stopping'] = CONFIG['mlp_early_stopping']
            model_params_final['validation_fraction'] = CONFIG['mlp_validation_fraction']
            model_params_final['n_iter_no_change'] = CONFIG['mlp_n_iter_no_change']
        if model_type_name == 'LogisticRegression' and 'solver' not in model_params_final:
            model_params_final['solver'] = CONFIG['lr_solver'] # Ensure solver is present
            
        # Instantiate and train the final classical model
        if model_type_name == 'ExtraTreesClassifier': final_model = ExtraTreesClassifier(**model_params_final)
        elif model_type_name == 'LogisticRegression': final_model = LogisticRegression(**model_params_final)
        elif model_type_name == 'SVC': final_model = SVC(**model_params_final)
        elif model_type_name == 'GradientBoostingClassifier': final_model = GradientBoostingClassifier(**model_params_final)
        elif model_type_name == 'RandomForestClassifier': final_model = RandomForestClassifier(**model_params_final)
        elif model_type_name == 'MLPClassifier': final_model = MLPClassifier(**model_params_final)
        elif model_type_name == 'KNeighborsClassifier': final_model = KNeighborsClassifier(**model_params_final)
        elif model_type_name == 'GaussianNB': final_model = GaussianNB(**model_params_final)
        
        if final_model:
            final_model.fit(X_train_full, y_train_full)
            print(f"{model_type_name} final model training complete.")
            plot_feature_importances_sklearn(final_model, feature_names, model_type_name, top_k=len(feature_names))
            # Evaluate classical model
            test_acc_f, _, test_pred_proba = evaluate_sklearn_model(final_model, X_test_data, y_test_data, model_type_name, "Original_Normalized", class_labels, results_list_summary_ref, return_probs=True)
            # Find the entry for this model in results_summary and update/add probas and params
            for res in results_list_summary_ref:
                if res['Model'] == model_type_name and res['Training_Data_Type'] == "Original_Normalized":
                    res['Test_Pred_Proba'] = test_pred_proba
                    res['Best_Params'] = best_trial.params # Store full optuna params
                    break
        else:
            print(f"Could not instantiate final model for {model_type_name}")
            results_list_summary_ref.append({'Model': model_type_name, 'Training_Data_Type': 'Original_Normalized', 'Test_Accuracy': 0.0, 'Best_Params': best_trial.params, 'Test_Pred_Proba': None})
            
    return final_model, best_trial.params # Return model and its HPs

trained_models = {}
model_best_hps = {}

if data_loaded_successfully and INPUT_SIZE > 0:
    # LSTM Experiment
    model, hps = run_model_experiment(
        "LSTM", 
        datasets_np['X_train_orig_norm'], datasets_np['y_train_orig'], 
        datasets_np['X_val_norm'], datasets_np['y_val'], 
        datasets_np['X_test_norm'], datasets_np['y_test'],
        results_summary, is_lstm=True
    )
    if model: trained_models["LSTM"] = model; model_best_hps["LSTM"] = hps

    # Classical Model Experiments
    classical_model_types = [
        'ExtraTreesClassifier', 'LogisticRegression', 'SVC', 
        'GradientBoostingClassifier', 'RandomForestClassifier', 
        'MLPClassifier', 'KNeighborsClassifier', 'GaussianNB'
    ]
    
    for model_name in classical_model_types:
        model, hps = run_model_experiment(
            model_name, 
            datasets_np['X_train_orig_norm'], datasets_np['y_train_orig'],
            datasets_np['X_val_norm'], datasets_np['y_val'], # X_val, y_val used for Optuna CV indirectly
            datasets_np['X_test_norm'], datasets_np['y_test'],
            results_summary, is_lstm=False
        )
        if model: trained_models[model_name] = model; model_best_hps[model_name] = hps
else:
    print("Skipping all experiments due to data loading issues or INPUT_SIZE=0.")

if data_loaded_successfully and INPUT_SIZE > 0 and trained_models:
    print("\n--- Generating Ensemble Predictions ---")
    all_test_probas = []
    model_weights = [] # Optional: for weighted averaging
    
    for result_entry in results_summary:
        model_name = result_entry['Model']
        # Ensure we only use models trained on 'Original_Normalized' for this ensemble
        if result_entry['Training_Data_Type'] == 'Original_Normalized' and result_entry['Test_Pred_Proba'] is not None:
            print(f"Adding predictions from: {model_name}")
            all_test_probas.append(result_entry['Test_Pred_Proba'])
            # Simple weighting by accuracy, can be more sophisticated
            model_weights.append(result_entry['Test_Accuracy'])
        elif result_entry['Training_Data_Type'] == 'Original_Normalized' and result_entry['Test_Pred_Proba'] is None:
            print(f"Warning: Test_Pred_Proba is None for {model_name}. It will be excluded from ensemble.")

    if not all_test_probas:
        print("No model probabilities available for ensembling.")
    else:
        # Normalize weights so they sum to 1 if using weighted average
        if sum(model_weights) > 0:
            normalized_weights = np.array(model_weights) / sum(model_weights)
            # Weighted average of probabilities
            avg_probas = np.zeros_like(all_test_probas[0])
            for i, probas in enumerate(all_test_probas):
                avg_probas += probas * normalized_weights[i]
            print("\nUsing Weighted Average Ensembling.")
        else: # Fallback to simple average if all weights are zero (e.g., all accuracies were 0)
            avg_probas = np.mean(all_test_probas, axis=0)
            print("\nUsing Simple Average Ensembling (weights were zero).")
            
        ensemble_preds = np.argmax(avg_probas, axis=1)

        ensemble_accuracy = accuracy_score(datasets_np['y_test'], ensemble_preds)
        print(f"\nEnsemble Test Accuracy: {ensemble_accuracy:.4f}")
        print("\nEnsemble Classification Report:")
        print(classification_report(datasets_np['y_test'], ensemble_preds, target_names=class_labels, zero_division=0))
        plot_confusion_matrix_generic(datasets_np['y_test'], ensemble_preds, "Ensemble Model - Test Set")
        
        results_summary.append({
            'Model': 'Ensemble (Avg Probs)',
            'Training_Data_Type': 'Original_Normalized',
            'Test_Accuracy': ensemble_accuracy,
            'Best_Params': 'N/A',
            'Test_Pred_Proba': avg_probas # Store ensemble probas too if needed
        })
else:
    print("Skipping ensembling due to data loading issues, INPUT_SIZE=0, or no models trained.")

if data_loaded_successfully and INPUT_SIZE > 0:
    print("\n--- All Model Results Summary ---")
    if results_summary:
        summary_df_all = pd.DataFrame(results_summary)
        summary_df_all = summary_df_all.sort_values(by='Test_Accuracy', ascending=False)
        
        print("\nAccuracy Metrics:")
        print(summary_df_all[['Model', 'Training_Data_Type', 'Test_Accuracy']])
        
        print("\nBest Parameters for each model (from Optuna):")
        for _, row in summary_df_all.iterrows():
            if row['Model'] != 'Ensemble (Avg Probs)':
                 print(f"--- {row['Model']} ({row['Training_Data_Type']}) ---")
                 # Check if Best_Params is a dict before trying to iterate
                 if isinstance(row['Best_Params'], dict):
                     for p_name, p_val in row['Best_Params'].items():
                         print(f"  {p_name}: {p_val}")
                 else:
                     print(f"  {row['Best_Params']}") # Print as is if not a dict (e.g., 'N/A')
                 print("\n")
        
        # Plotting comparison
        plt.figure(figsize=(12, 10))
        # Filter out SMOTEd results if any were accidentally added, as per request to use original only
        plot_df = summary_df_all[summary_df_all['Training_Data_Type'] == 'Original_Normalized']
        sns.barplot(x='Test_Accuracy', y='Model', data=plot_df, dodge=False, color='skyblue') # Removed hue
        plt.title('Model Test Accuracies (Trained on Original Normalized Data)')
        plt.xlabel('Test Accuracy')
        plt.ylabel('Model')
        plt.xlim(0, 1.0) 
        plt.tight_layout()
        plt.show()
    else:
        print("No model results to summarize.")
else:
    print("Results summary cannot be generated as data was not loaded or INPUT_SIZE was 0.")